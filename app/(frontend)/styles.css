:root {
  --font-mono: 'Roboto Mono', monospace;
}

* {
  box-sizing: border-box;
}

html {
  font-size: 18px;
  line-height: 32px;

  background: rgb(0, 0, 0);
  -webkit-font-smoothing: antialiased;
}

html,
body,
#app {
  height: 100%;
}

body {
  font-family: system-ui;
  font-size: 18px;
  line-height: 32px;

  margin: 0;
  color: rgb(1000, 1000, 1000);

  @media (max-width: 1024px) {
    font-size: 15px;
    line-height: 24px;
  }
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

h1 {
  margin: 40px 0;
  font-size: 64px;
  line-height: 70px;
  font-weight: bold;

  @media (max-width: 1024px) {
    margin: 24px 0;
    font-size: 42px;
    line-height: 42px;
  }

  @media (max-width: 768px) {
    font-size: 38px;
    line-height: 38px;
  }

  @media (max-width: 400px) {
    font-size: 32px;
    line-height: 32px;
  }
}

p {
  margin: 24px 0;

  @media (max-width: 1024px) {
    margin: calc(var(--base) * 0.75) 0;
  }
}

a {
  color: currentColor;

  &:focus {
    opacity: 0.8;
    outline: none;
  }

  &:active {
    opacity: 0.7;
    outline: none;
  }
}

svg {
  vertical-align: middle;
}

.home {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 100vh;
  padding: 45px;
  max-width: 1024px;
  margin: 0 auto;
  overflow: hidden;

  @media (max-width: 400px) {
    padding: 24px;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-grow: 1;

    h1 {
      text-align: center;
    }
  }

  .links {
    display: flex;
    align-items: center;
    gap: 12px;

    a {
      text-decoration: none;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
    }

    .admin {
      color: rgb(0, 0, 0);
      background: rgb(1000, 1000, 1000);
      border: 1px solid rgb(0, 0, 0);
    }

    .docs {
      color: rgb(1000, 1000, 1000);
      background: rgb(0, 0, 0);
      border: 1px solid rgb(1000, 1000, 1000);
    }
  }

  .footer {
    display: flex;
    align-items: center;
    gap: 8px;

    @media (max-width: 1024px) {
      flex-direction: column;
      gap: 6px;
    }

    p {
      margin: 0;
    }

    .codeLink {
      text-decoration: none;
      padding: 0 0.5rem;
      background: rgb(60, 60, 60);
      border-radius: 4px;
    }
  }
}
