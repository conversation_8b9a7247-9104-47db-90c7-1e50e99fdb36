@tailwind base;
@tailwind components;
@tailwind utilities;

body {
	font-family: Arial, Helvetica, sans-serif;
}

@layer base {
	:root {
		--background: 0 0% 100%; /* White */
		--foreground: 0 0% 0%; /* Black */
		--card: 0 0% 100%; /* White */
		--card-foreground: 0 0% 0%; /* Black */
		--popover: 0 0% 100%; /* White */
		--popover-foreground: 0 0% 0%; /* Black */
		--primary: 330 100% 50%; /* Pink #FF0066 */
		--primary-foreground: 0 0% 100%; /* White */
		--secondary: 0 0% 96%; /* Light gray */
		--secondary-foreground: 0 0% 0%; /* Black */
		--muted: 0 0% 96%; /* Light gray */
		--muted-foreground: 0 0% 30%; /* Darker gray */
		--accent: 0 0% 96%; /* Light gray */
		--accent-foreground: 0 0% 0%; /* Black */
		--destructive: 0 100% 50%; /* Red */
		--destructive-foreground: 0 0% 100%; /* White */
		--border: 0 0% 85%; /* Gray border */
		--input: 0 0% 85%; /* Gray input */
		--ring: 330 100% 50%; /* Pink #FF0066 */
		--radius: 1rem;
		--chart-1: 330 100% 50%; /* Pink */
		--chart-2: 0 0% 0%; /* Black */
		--chart-3: 0 0% 30%; /* Dark gray */
		--chart-4: 0 0% 60%; /* Medium gray */
		--chart-5: 0 0% 80%; /* Light gray */

		/* Additional Brand Pink Shades */
		--pink-50: 330 100% 97%; /* Lightest pink */
		--pink-100: 330 100% 94%;
		--pink-200: 330 100% 88%;
		--pink-300: 330 100% 82%;
		--pink-400: 330 100% 66%;
		--pink-500: 330 100% 50%; /* Your brand pink #FF0066 */
		--pink-600: 330 100% 44%;
		--pink-700: 330 100% 38%;
		--pink-800: 330 100% 32%;
		--pink-900: 330 100% 26%; /* Darkest pink */

		/* Additional Gray Shades */
		--gray-50: 0 0% 98%;
		--gray-100: 0 0% 96%;
		--gray-200: 0 0% 90%;
		--gray-300: 0 0% 85%;
		--gray-400: 0 0% 65%;
		--gray-500: 0 0% 45%;
		--gray-600: 0 0% 35%;
		--gray-700: 0 0% 25%;
		--gray-800: 0 0% 15%;
		--gray-900: 0 0% 10%;

		/* Success Colors */
		--success-50: 142 72% 95%;
		--success-500: 142 72% 29%;
		--success-900: 142 72% 15%;

		/* Warning Colors */
		--warning-50: 38 92% 95%;
		--warning-500: 38 92% 50%;
		--warning-900: 38 92% 25%;

		/* Info Colors */
		--info-50: 206 100% 95%;
		--info-500: 206 100% 50%;
		--info-900: 206 100% 25%;
	}

	.dark {
		--background: 0 0% 0%; /* Black */
		--foreground: 0 0% 100%; /* White */
		--card: 0 0% 10%; /* Dark gray */
		--card-foreground: 0 0% 100%; /* White */
		--popover: 0 0% 10%; /* Dark gray */
		--popover-foreground: 0 0% 100%; /* White */
		--primary: 330 100% 50%; /* Pink #FF0066 */
		--primary-foreground: 0 0% 100%; /* White */
		--secondary: 0 0% 15%; /* Dark gray */
		--secondary-foreground: 0 0% 100%; /* White */
		--muted: 0 0% 15%; /* Dark gray */
		--muted-foreground: 0 0% 60%; /* Light gray */
		--accent: 0 0% 20%; /* Darker gray */
		--accent-foreground: 0 0% 100%; /* White */
		--destructive: 0 100% 40%; /* Darker red */
		--destructive-foreground: 0 0% 100%; /* White */
		--border: 0 0% 30%; /* Dark border */
		--input: 0 0% 30%; /* Dark input */
		--ring: 330 100% 50%; /* Pink #FF0066 */
		--chart-1: 330 100% 50%; /* Pink */
		--chart-2: 0 0% 100%; /* White */
		--chart-3: 0 0% 80%; /* Light gray */
		--chart-4: 0 0% 50%; /* Medium gray */
		--chart-5: 0 0% 30%; /* Dark gray */

		/* Dark theme adjustments for new colors */
		--success-50: 142 72% 20%;
		--success-500: 142 72% 35%;
		--success-900: 142 72% 10%;

		--warning-50: 38 92% 20%;
		--warning-500: 38 92% 45%;
		--warning-900: 38 92% 15%;

		--info-50: 206 100% 20%;
		--info-500: 206 100% 45%;
		--info-900: 206 100% 15%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

@layer utilities {
	.underlined-link {
		@apply underline hover:text-primary/90;
	}
}



@layer base {
  * {
    @apply border-border outline-ring/50;
	}
  body {
    @apply bg-background text-foreground;
	}
}
