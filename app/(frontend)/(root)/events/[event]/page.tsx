import {
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  ArrowLeft,
  Calendar,
  Users,
  Globe,
  Share2,
  ExternalLink,
  Download,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import { eventsData, Event } from "@/data/eventsData";

// Function to get event by ID
const getEventById = (id: string): Event | undefined => {
  return eventsData.find((event) => event.id === id);
};

// Generate static params for all events
export async function generateStaticParams() {
  return eventsData.map((event) => ({
    event: event.id,
  }));
}

// Generate metadata for each event
export async function generateMetadata({
  params,
}: {
  params: Promise<{ event: string }>;
}): Promise<Metadata> {
  const { event } = await params;
  const eventData = getEventById(event);

  if (!eventData) {
    return {
      title: "Event Not Found",
      description: "The requested event could not be found.",
    };
  }

  return {
    title: `${eventData.title} | GirlCode Movement`,
    description: eventData.description,
    openGraph: {
      title: eventData.title,
      description: eventData.description,
      images: [eventData.image],
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: eventData.title,
      description: eventData.description,
      images: [eventData.image],
    },
  };
}

export default async function EventPage({
  params,
}: {
  params: Promise<{ event: string }>;
}) {
  const { event: eventId } = await params;
  const event = getEventById(eventId);

  // Not found state
  if (!event) {
    notFound();
  }
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Navigation Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <Link
            href="/events"
            className="inline-flex items-center text-primary hover:text-primary/80 font-medium transition-colors group"
          >
            <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
            <span>Back to Events</span>
          </Link>
        </div>
      </div>
      {/* Hero Section with Enhanced Design */}
      <div className="relative w-full h-[400px] md:h-[500px] overflow-hidden">
        <Image
          src={event.image}
          alt={event.title}
          fill
          className="object-cover"
          loading="lazy"
          priority={false}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
          <div className="absolute bottom-0 left-0 right-0 p-8">
            <div className="container mx-auto">
              <div className="flex items-center gap-3 mb-4">
                <span
                  className={`px-3 py-1.5 rounded-full text-white text-sm font-medium ${
                    event.status === "upcoming" ? "bg-green-500" : "bg-gray-500"
                  }`}
                >
                  {event.status === "upcoming"
                    ? "🎯 Upcoming Event"
                    : "📅 Past Event"}
                </span>
                <span className="px-3 py-1.5 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm font-medium">
                  {event.category}
                </span>
              </div>
              <h1 className="text-white text-3xl md:text-5xl font-bold mb-4 leading-tight">
                {event.title}
              </h1>
              <p className="text-white/90 text-lg md:text-xl max-w-2xl">
                {event.excerpt ||
                  "Join us for an inspiring and impactful event"}
              </p>
            </div>
          </div>
        </div>
      </div>{" "}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Event Overview Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="bg-primary/10 p-3 rounded-xl">
                  <Calendar className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-1">
                    Event Overview
                  </h2>
                  <p className="text-gray-500">
                    Everything you need to know about this event
                  </p>
                </div>
              </div>

              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed text-lg mb-6">
                  {event.description}
                </p>

                {event.status === "upcoming" && (
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-lg p-6 my-8">
                    <h3 className="text-lg font-semibold text-blue-900 mb-3">
                      🚀 What to Expect
                    </h3>
                    <p className="text-blue-800 leading-relaxed">
                      Join our vibrant community for an engaging experience
                      designed to inspire, educate, and connect. This event
                      brings together like-minded individuals passionate about
                      technology, innovation, and positive change.
                    </p>
                  </div>
                )}

                {event.status === "past" && (
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-100 rounded-lg p-6 my-8">
                    <h3 className="text-lg font-semibold text-green-900 mb-3">
                      ✨ Event Recap
                    </h3>
                    <p className="text-green-800 leading-relaxed">
                      This event was a tremendous success, bringing together our
                      community for meaningful connections, learning
                      opportunities, and inspiring discussions about the future
                      of technology.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Event Highlights */}
            {event.highlights && event.highlights.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-yellow-100 p-3 rounded-xl">
                    <div className="w-6 h-6 text-yellow-600">⭐</div>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">
                      Key Highlights
                    </h2>
                    <p className="text-gray-500">
                      What makes this event special
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {event.highlights.map((highlight, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg"
                    >
                      <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-gray-700 leading-relaxed">
                        {highlight}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Event Agenda */}
            {event.agenda && event.agenda.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-purple-100 p-3 rounded-xl">
                    <Clock className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">
                      Event Agenda
                    </h2>
                    <p className="text-gray-500">Schedule and timeline</p>
                  </div>
                </div>
                <div className="space-y-4">
                  {event.agenda.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-4 p-4 border border-gray-100 rounded-lg"
                    >
                      <div className="w-8 h-8 bg-primary/10 text-primary font-semibold text-sm rounded-full flex items-center justify-center">
                        {index + 1}
                      </div>
                      <p className="text-gray-700">{item}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>{" "}
          {/* Enhanced Sidebar - Event Details */}
          <div className="lg:col-span-1">
            {/* Quick Event Info Card */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6 sticky top-6">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
                  <CalendarIcon className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">
                  Event Details
                </h3>
                <p className="text-gray-500 text-sm mt-1">
                  Essential information
                </p>
              </div>

              {/* Date & Time */}
              <div className="space-y-4 mb-6">
                <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <Calendar className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 mb-1">
                      Date & Time
                    </p>
                    <p className="text-sm text-gray-600">{event.date}</p>
                    <p className="text-sm text-gray-600">{event.time}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                  <div className="bg-green-100 p-2 rounded-lg">
                    <MapPin className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 mb-1">Location</p>
                    <p className="text-sm text-gray-600">{event.location}</p>
                    <button className="text-primary text-sm font-medium mt-2 hover:text-primary/80 transition-colors">
                      Get directions →
                    </button>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                  <div className="bg-purple-100 p-2 rounded-lg">
                    <Users className="w-5 h-5 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 mb-1">Category</p>
                    <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                      {event.category}
                    </span>
                  </div>
                </div>
              </div>

              {/* Registration Section */}
              {event.status === "upcoming" && event.registration && (
                <div className="border-t border-gray-200 pt-6">
                  <div className="bg-gradient-to-r from-primary to-primary/80 rounded-lg p-4 text-white mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">
                        Registration Fee
                      </span>
                      <span className="text-lg font-bold">
                        {event.registration.fee}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Deadline:</span>
                      <span>{event.registration.deadline}</span>
                    </div>
                  </div>

                  <a
                    href={event.registration.url || "#"}
                    className="block w-full bg-primary hover:bg-primary/90 text-white text-center font-medium py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                  >
                    🎯 Register Now
                  </a>
                </div>
              )}

              {/* Past Event Action */}
              {event.status === "past" && (
                <div className="border-t border-gray-200 pt-6">
                  <div className="bg-gradient-to-r from-gray-500 to-gray-600 rounded-lg p-4 text-white mb-4">
                    <div className="text-center">
                      <span className="text-sm font-medium">
                        📅 Event Completed
                      </span>
                    </div>
                  </div>

                  <button className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-800 text-center font-medium py-3 px-4 rounded-lg transition-colors">
                    📸 View Event Photos
                  </button>
                </div>
              )}

              {/* Additional Actions */}
              <div className="border-t border-gray-200 pt-6 mt-6">
                <div className="space-y-3">
                  <button className="w-full flex items-center justify-center gap-2 bg-gray-50 hover:bg-gray-100 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors">
                    <Calendar className="w-4 h-4" />
                    Add to Calendar
                  </button>
                  <button className="w-full flex items-center justify-center gap-2 bg-gray-50 hover:bg-gray-100 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors">
                    <Users className="w-4 h-4" />
                    Invite Friends
                  </button>
                </div>
              </div>
            </div>

            {/* Event Stats */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Event Impact</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">150+</div>
                  <div className="text-sm text-gray-600">
                    Expected Attendees
                  </div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">3</div>
                  <div className="text-sm text-gray-600">SDG Goals</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Bottom Sections - UN SDG Impact and Share Event */}
      <div className="bg-white border-t border-gray-100">
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* UN SDG Impact Section */}
            {event.relatedSDGs && event.relatedSDGs.length > 0 && (
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100 p-8">
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-green-100 p-3 rounded-xl">
                    <Globe className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      UN SDG Impact
                    </h3>
                    <p className="text-gray-600">
                      Supporting Sustainable Development Goals
                    </p>
                  </div>
                </div>
                <p className="text-gray-700 mb-6 leading-relaxed">
                  This event directly contributes to the United Nations
                  Sustainable Development Goals, helping create a more
                  sustainable and equitable future for all.
                </p>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {event.relatedSDGs.map((sdg) => (
                    <div
                      key={sdg}
                      className="bg-white rounded-lg p-3 border border-green-200 text-center hover:shadow-md transition-shadow"
                    >
                      <div className="text-2xl mb-1">🌍</div>
                      <span className="text-sm font-medium text-green-800">
                        SDG {sdg}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-6">
                  <Link
                    href="https://sdgs.un.org/goals"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-green-600 hover:text-green-700 font-medium transition-colors"
                  >
                    Learn more about SDGs
                    <ExternalLink className="w-4 h-4" />
                  </Link>
                </div>
              </div>
            )}

            {/* Share Event Section */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100 p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="bg-blue-100 p-3 rounded-xl">
                  <Share2 className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">
                    Share This Event
                  </h3>
                  <p className="text-gray-600">
                    Help us spread the word about this amazing event
                  </p>
                </div>
              </div>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Share this event with your network and help us reach more people
                who can benefit from this opportunity to learn, grow, and
                connect.
              </p>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <button className="flex items-center justify-center gap-3 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg transition-colors font-medium">
                  <span className="text-xl">📘</span>
                  Facebook
                </button>
                <button className="flex items-center justify-center gap-3 bg-sky-500 hover:bg-sky-600 text-white p-4 rounded-lg transition-colors font-medium">
                  <span className="text-xl">🐦</span>
                  Twitter
                </button>
                <button className="flex items-center justify-center gap-3 bg-blue-700 hover:bg-blue-800 text-white p-4 rounded-lg transition-colors font-medium">
                  <span className="text-xl">💼</span>
                  LinkedIn
                </button>
                <button className="flex items-center justify-center gap-3 bg-gray-600 hover:bg-gray-700 text-white p-4 rounded-lg transition-colors font-medium">
                  <span className="text-xl">🔗</span>
                  Copy Link
                </button>
              </div>
              <div className="flex items-center gap-4">
                <button className="flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium transition-colors">
                  <Download className="w-4 h-4" />
                  Download Event Flyer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Related Events Section */}
      <div className="bg-gray-50 border-t border-gray-100">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              You Might Also Like
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover other exciting events that align with your interests and
              career goals
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {eventsData
              .filter(
                (relatedEvent) =>
                  relatedEvent.id !== event.id &&
                  (relatedEvent.category === event.category ||
                    relatedEvent.status === "upcoming")
              )
              .slice(0, 3)
              .map((relatedEvent) => (
                <Link
                  key={relatedEvent.id}
                  href={`/events/${relatedEvent.id}`}
                  className="group bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200"
                >
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={relatedEvent.image}
                      alt={relatedEvent.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                    <div className="absolute top-4 left-4">
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          relatedEvent.status === "upcoming"
                            ? "bg-green-100 text-green-700"
                            : "bg-gray-100 text-gray-700"
                        }`}
                      >
                        {relatedEvent.status === "upcoming"
                          ? "Upcoming"
                          : "Past"}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      <CalendarIcon className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-500">
                        {new Date(relatedEvent.date).toLocaleDateString(
                          "en-US",
                          {
                            month: "short",
                            day: "numeric",
                            year: "numeric",
                          }
                        )}
                      </span>
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-2 group-hover:text-primary transition-colors line-clamp-2">
                      {relatedEvent.title}
                    </h4>
                    <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                      {relatedEvent.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {relatedEvent.category}
                      </span>
                      <span className="text-primary text-sm font-medium group-hover:translate-x-1 transition-transform">
                        Learn more →
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}
