import { Metadata } from "next";
import React from "react";
import HeroSec<PERSON> from "@/components/events/HeroSection";
import EventsDisplay from "@/components/events/EventsDisplay";
import CallToAction from "@/components/events/CallToAction";
import { generateSEOMetadata, generateBreadcrumbSchema } from "@/lib/seo";

export const metadata: Metadata = generateSEOMetadata({
  title: "Events - Workshops, Bootcamps & Community Gatherings",
  description:
    "Join GirlCode Movement's upcoming events including coding bootcamps, tech workshops, climate hackathons, and women's leadership summits. Connect with the community and advance your skills through hands-on learning experiences.",
  keywords: [
    "coding bootcamp events",
    "tech workshops",
    "women in tech events",
    "climate hackathon",
    "leadership summit",
    "training events",
    "community workshops",
    "technology events Kenya",
    "women empowerment events",
    "digital skills workshops",
    "career development events",
    "networking events",
  ],
  canonical: "/events",
});

export default function events() {
  return (
    <main className="min-h-screen bg-white">
      <HeroSection />
      <EventsDisplay />
      <CallToAction />
    </main>
  );
}
