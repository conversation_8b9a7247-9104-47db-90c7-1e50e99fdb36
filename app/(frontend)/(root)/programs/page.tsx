import { Metadata } from "next";
import HeroSection from "@/components/programs/HeroSection";
import ProgramPathways from "@/components/programs/ProgramPathways";
import ProgramTestimonials from "@/components/programs/ProgramTestimonials";
import ProgramApplicationCTA from "@/components/programs/ProgramApplicationCTA";
import ResourceLibrary from "@/components/programs/ResourceLibrary";
import FAQ from "@/components/programs/FAQ";
import { generateSEOMetadata, generateBreadcrumbSchema } from "@/lib/seo";

export const metadata: Metadata = generateSEOMetadata({
  title: "Programs - Quality Education & Skills Training for Women",
  description:
    "Explore GirlCode Movement's comprehensive programs including coding bootcamps, 3D printing workshops, financial literacy training, and mental health support. Join thousands of women advancing their careers through our technology and life skills programs.",
  keywords: [
    "coding bootcamps",
    "3D printing training",
    "digital skills program",
    "financial literacy workshops",
    "women tech training",
    "career development",
    "mental health support",
    "SDG education programs",
    "tech skills for women",
    "coding for beginners",
    "professional development",
    "women empowerment training",
  ],
  canonical: "/programs",
});

export default function programs() {
  return (
    <div>
      <HeroSection />
      <ProgramPathways />
      <ProgramTestimonials />
      <ProgramApplicationCTA />
      <ResourceLibrary />
      <FAQ />
    </div>
  );
}
