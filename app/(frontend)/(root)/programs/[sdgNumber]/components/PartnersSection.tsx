// app/programs/sdg-[number]/components/PartnersSection.tsx
import Image from "next/image";
import { SDGData, Partner } from "@/types/sdg";

interface PartnersSectionProps {
  sdg: SDGData;
}

function PartnerCard({ partner }: { partner: Partner }) {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all flex flex-col items-center text-center">
      <div className="w-24 h-24 relative mb-4">
        <Image 
          src={partner.logo}
          alt={partner.name}
          fill
          className="object-contain"
        />
      </div>
      <h3 className="font-bold text-lg mb-2">{partner.name}</h3>
      <p className="text-gray-600 text-sm mb-4">{partner.description}</p>
      <a 
        href={partner.website} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-primary hover:underline text-sm font-medium"
      >
        Visit Website
      </a>
    </div>
  );
}

export default function PartnersSection({ sdg }: PartnersSectionProps) {
  if (sdg.partners.length === 0) {
    return null;
  }

  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-12 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-4">
            <span className="text-sm font-medium text-primary">Our Partners</span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            Working Together for {sdg.title}
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            We collaborate with these organizations to maximize our impact in {sdg.title.toLowerCase()} initiatives.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sdg.partners.map((partner) => (
            <PartnerCard key={partner.id} partner={partner} />
          ))}
        </div>

        <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border border-gray-100 text-center">
          <h3 className="font-bold text-xl mb-2">Interested in Partnering with Us?</h3>
          <p className="text-gray-600 mb-4 max-w-2xl mx-auto">
            We{"'"}re always looking for new partners who share our vision for sustainable development and 
            want to make a difference in the {sdg.title.toLowerCase()} space.
          </p>
          <a 
            href="/contact" 
            className="inline-block bg-primary text-white px-4 py-2 rounded hover:bg-primary/90 transition-colors"
          >
            Contact Us
          </a>
        </div>
      </div>
    </div>
  );
}