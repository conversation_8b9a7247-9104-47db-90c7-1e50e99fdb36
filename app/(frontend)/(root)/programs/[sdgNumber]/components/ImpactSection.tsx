// app/programs/sdg-[number]/components/ImpactSection.tsx
import Image from "next/image";
import { SDGData, ImpactMetric } from "@/types/sdg";

interface ImpactSectionProps {
  sdg: SDGData;
}

function ImpactCard({ metric, sdg }: { metric: ImpactMetric; sdg: SDGData }) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 flex flex-col items-center text-center hover:shadow-md transition-all">
      <div className={`${sdg.iconBgColor} rounded-full p-4 mb-4`}>
        <Image
          src={metric.icon}
          alt={metric.title}
          width={28}
          height={28}
          className="w-7 h-7"
        />
      </div>
      <h3 className="text-xl font-bold mb-1">{metric.title}</h3>
      <p className={`text-3xl font-bold ${sdg.textColor} mb-3`}>{metric.value}</p>
      <p className="text-gray-600 text-sm">{metric.description}</p>
    </div>
  );
}

export default function ImpactSection({ sdg }: ImpactSectionProps) {
  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-white border-t border-gray-100">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-12 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-4">
            <span className="text-sm font-medium text-primary">Our Impact</span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            Transforming Lives Through {sdg.title}
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Measuring our progress and impact in creating sustainable change through our
            SDG {sdg.number} initiatives.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {sdg.impactMetrics.map((metric) => (
            <ImpactCard key={metric.id} metric={metric} sdg={sdg} />
          ))}
        </div>

        <div className={`mt-12 ${sdg.bgColor} rounded-xl p-8 border ${sdg.borderColor}`}>
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="md:w-1/4 flex justify-center">
              <div className={`${sdg.iconBgColor} rounded-full p-6`}>
                <Image
                  src={sdg.icon}
                  alt={sdg.title}
                  width={60}
                  height={60}
                  className="w-14 h-14"
                />
              </div>
            </div>

            <div className="md:w-3/4">
              <h3 className="text-xl font-bold mb-4">
                Our Commitment to Sustainable Impact
              </h3>
              <p className="text-gray-700 mb-4">
                {sdg.number === 3 && "Our mental health and wellbeing programs are designed to create lasting change in communities. By focusing on resilience, education, and support systems, we empower individuals to maintain their mental health and support others."}
                {sdg.number === 4 && "Through quality education initiatives, we're creating a foundation for lifelong learning and economic independence. Our programs emphasis practical skills, financial literacy, and technological fluency to open doors to opportunity."}
                {sdg.number === 5 && "Gender equality requires both immediate interventions and systemic change. Our programs provide direct support to women and girls while advocating for structural changes that address discrimination and violence at their roots."}
                {sdg.number === 13 && "Climate action is most effective when it combines education, innovation, and community involvement. Our programs teach sustainable practices while developing solutions to environmental challenges that can be implemented locally and scaled globally."}
              </p>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${sdg.buttonColor}`}></div>
                <p className="font-medium">
                  Aligned with UN Sustainable Development Goal {sdg.number}: {sdg.title}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}