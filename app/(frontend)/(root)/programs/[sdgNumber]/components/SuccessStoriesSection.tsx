// app/programs/sdg-[number]/components/SuccessStoriesSection.tsx
import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SDGData, SuccessStory } from "@/types/sdg";

interface SuccessStoriesSectionProps {
  sdg: SDGData;
}

function FeaturedStoryCard({ story, sdg }: { story: SuccessStory; sdg: SDGData }) {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mb-12">
      <div className="md:flex">
        <div className="md:w-2/5 relative">
          <div className="h-64 md:h-full relative">
            <Image
              src={story.personImage}
              alt={story.personName}
              fill
              className="object-cover"
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent md:bg-gradient-to-r"></div>
          <div className="absolute bottom-4 left-4 md:bottom-8 md:left-8 text-white">
            <h3 className="font-bold text-xl mb-1">{story.personName}</h3>
            <p className="text-sm text-white/80">{story.personRole}</p>
          </div>
        </div>
        <div className="md:w-3/5 p-6 md:p-8">
          <div className={`inline-block ${sdg.bgColor} ${sdg.textColor} text-xs px-3 py-1 rounded-full mb-4`}>
            Featured Story
          </div>
          <h2 className="text-2xl font-bold mb-3">{story.title}</h2>
          <p className="text-gray-500 mb-4">{story.summary}</p>
          
          <div className="mb-6">
            <h4 className="font-medium text-sm mb-2">Impact:</h4>
            <ul className="space-y-1">
              {story.impact.map((point, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                  <div className={`w-1.5 h-1.5 rounded-full ${sdg.buttonColor} mt-1.5`}></div>
                  <span>{point}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <Button
            variant="outline"
            className={`${sdg.textColor} border-${sdg.color.replace('bg-', '')} hover:bg-${sdg.color.replace('bg-', '')}/5`}
          >
            <Link href={`/success-stories/${story.id}`} className="flex items-center gap-2">
              <span>Read Full Story</span>
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}

function StoryCard({ story, sdg }: { story: SuccessStory; sdg: SDGData }) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all">
      <div className="relative h-56">
        <Image
          src={story.personImage}
          alt={story.personName}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
        <div className="absolute bottom-4 left-4 text-white">
          <h3 className="font-bold text-lg mb-1">{story.personName}</h3>
          <p className="text-xs text-white/80">{story.personRole}</p>
        </div>
      </div>
      <div className="p-6">
        <h3 className="font-bold text-xl mb-2">{story.title}</h3>
        <p className="text-gray-600 text-sm mb-4">{story.summary}</p>
        
        <div className="mb-6">
          <h4 className="font-medium text-xs mb-2">Key Impact:</h4>
          <p className="text-sm text-gray-600">{story.impact[0]}</p>
        </div>
        
        <Button
          variant="ghost"
          className={`${sdg.textColor} hover:bg-${sdg.color.replace('bg-', '')}/5 w-full justify-center`}
        >
          <Link href={`/success-stories/${story.id}`} className="flex items-center gap-2">
            <span>Read Story</span>
            <ChevronRight className="h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  );
}

export default function SuccessStoriesSection({ sdg }: SuccessStoriesSectionProps) {
  if (sdg.successStories.length === 0) {
    return null;
  }

  const featuredStory = sdg.successStories.find(story => story.featured);
  const otherStories = sdg.successStories.filter(story => !story.featured);

  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-8 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-4">
            <span className="text-sm font-medium text-primary">Success Stories</span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">
            Transformational Journeys
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Meet the people whose lives have been transformed through our {sdg.title.toLowerCase()} initiatives.
          </p>
        </div>

        {featuredStory && (
          <FeaturedStoryCard story={featuredStory} sdg={sdg} />
        )}

        {otherStories.length > 0 && (
          <>
            <h3 className="font-bold text-xl mb-6 flex items-center">
              <div className="bg-primary/10 p-2 rounded-full mr-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </div>
              More Success Stories
            </h3>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {otherStories.map(story => (
                <StoryCard key={story.id} story={story} sdg={sdg} />
              ))}
            </div>
          </>
        )}

        <div className="mt-12 text-center">
          <Button 
            variant="outline"
            className="border-primary text-primary hover:bg-primary/5"
          >
            <Link href="/success-stories" className="flex items-center gap-2">
              View All Success Stories
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}