// app/programs/[sdgNumber]/components/SDGPage.tsx
"use client";

import { useEffect, useState } from "react";
import sdgData from "@/data/sdgData";
import { SDGData } from "@/types/sdg";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

// Import all section components
import {
  HeroSection,
  OverviewSection,
  ImpactSection,
  TestimonialsSection,
  ResourcesSection,
  RelatedBlogsSection,
  UpcomingEventsSection,
  CallToActionSection,
  PartnersSection,
  SuccessStoriesSection,
  MediaGallerySection,
  FAQSection,
  RelatedSDGsSection,
} from ".";

interface SDGPageProps {
  sdgNumber: string;
}

export default function SDGPage({ sdgNumber }: SDGPageProps) {
  const [data, setData] = useState<SDGData | null>(null);
  const [loading, setLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState<{
    paramValue: string;
    availableKeys: string[];
    dataFound: boolean;
  } | null>(null);

  useEffect(() => {
    // Use the sdgNumber prop directly
    const sdgKey = sdgNumber;

    // For debugging
    const availableKeys = Object.keys(sdgData);
    const dataFound = !!sdgData[sdgKey];

    setDebugInfo({
      paramValue: sdgKey,
      availableKeys,
      dataFound,
    });

    if (sdgData[sdgKey]) {
      setData(sdgData[sdgKey]);
    } else {
      // Try with different formats
      // If the key is just "3" but our data uses "sdg-3"
      if (sdgKey.match(/^\d+$/) && sdgData[`sdg-${sdgKey}`]) {
        setData(sdgData[`sdg-${sdgKey}`]);
      }
      // If the key is "sdg-3" but our data uses just "3"
      else if (sdgKey.startsWith("sdg-") && sdgData[sdgKey.substring(4)]) {
        setData(sdgData[sdgKey.substring(4)]);
      }
    }

    setLoading(false);
  }, [sdgNumber]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        Loading...
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen">
        <h1 className="text-3xl font-bold mb-4">SDG Not Found</h1>
        <p className="mb-6">
          Sorry, we couldn{"'"}t find information about this SDG.
        </p>

        {/* Debug information */}
        <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-6 max-w-lg">
          <h2 className="font-bold mb-2">Debug Information:</h2>
          <p>
            <strong>Parameter value:</strong>{" "}
            {debugInfo?.paramValue || "unknown"}
          </p>
          <p>
            <strong>Available data keys:</strong>{" "}
            {debugInfo?.availableKeys.join(", ") || "none"}
          </p>
          <p>
            <strong>Data found:</strong> {debugInfo?.dataFound ? "Yes" : "No"}
          </p>
        </div>

        <Button>
          <Link href="/programs">Return to Programs</Link>
        </Button>
      </div>
    );
  }

  return (
    <div>
      <HeroSection sdg={data} />
      <OverviewSection sdg={data} />
      {/* <ProgramsSection sdg={data} /> */}
      <ImpactSection sdg={data} />
      {/* <SuccessStoriesSection sdg={data} /> */}
      {/* <TestimonialsSection sdg={data} /> */}
      <MediaGallerySection sdg={data} />
      {/* <ResourcesSection sdg={data} /> */}
      <PartnersSection sdg={data} />
      {/* <RelatedBlogsSection sdg={data} /> */}
      {/* <UpcomingEventsSection sdg={data} /> */}
      {/* <FAQSection sdg={data} /> */}
      <RelatedSDGsSection sdg={data} />
      {/* <CallToActionSection sdg={data} /> */}
    </div>
  );
}
