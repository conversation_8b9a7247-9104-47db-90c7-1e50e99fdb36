// app/programs/sdg-[number]/components/RelatedSDGsSection.tsx
import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { SDGData, RelatedSDG } from "@/types/sdg";

interface RelatedSDGsSectionProps {
  sdg: SDGData;
}

function RelatedSDGCard({ relatedSDG }: { relatedSDG: RelatedSDG }) {
  // Define colors based on SDG number
  const getSDGColors = (number: number) => {
    switch (number) {
      case 3:
        return {
          bgColor: "bg-purple-50",
          borderColor: "border-purple-100",
          textColor: "text-purple-600",
          buttonColor: "bg-purple-600",
        };
      case 4:
        return {
          bgColor: "bg-blue-50",
          borderColor: "border-blue-100",
          textColor: "text-blue-600",
          buttonColor: "bg-blue-600",
        };
      case 5:
        return {
          bgColor: "bg-pink-50",
          borderColor: "border-pink-100",
          textColor: "text-pink-600",
          buttonColor: "bg-pink-600",
        };
      case 8:
        return {
          bgColor: "bg-red-50",
          borderColor: "border-red-100",
          textColor: "text-red-600",
          buttonColor: "bg-red-600",
        };
      case 12:
        return {
          bgColor: "bg-amber-50",
          borderColor: "border-amber-100",
          textColor: "text-amber-600",
          buttonColor: "bg-amber-600",
        };
      case 13:
        return {
          bgColor: "bg-green-50",
          borderColor: "border-green-100",
          textColor: "text-green-600",
          buttonColor: "bg-green-600",
        };
      default:
        return {
          bgColor: "bg-gray-50",
          borderColor: "border-gray-100",
          textColor: "text-gray-600",
          buttonColor: "bg-gray-600",
        };
    }
  };

  const colors = getSDGColors(relatedSDG.number);

  return (
    <div
      className={`${colors.bgColor} rounded-xl shadow-sm border ${colors.borderColor} overflow-hidden hover:shadow-md transition-all`}
    >
      <div className="relative h-48">
        <Image
          src={relatedSDG.image}
          alt={`SDG ${relatedSDG.number}: ${relatedSDG.title}`}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
        <div className="absolute bottom-4 left-4">
          <div
            className={`${colors.buttonColor}  px-2 py-1 text-white text-xs font-bold inline-flex items-center gap-1 mb-1`}
          >
            SDG {relatedSDG.number}
          </div>
          <h3 className="text-white font-bold text-lg">{relatedSDG.title}</h3>
        </div>
      </div>
      <div className="p-6">
        <h4 className="font-medium text-sm mb-2">Connection:</h4>
        <p className="text-gray-600 text-sm mb-4">{relatedSDG.connection}</p>
        <Link
          href={`/programs/sdg-${relatedSDG.number}`}
          className={`inline-flex items-center gap-1 ${colors.textColor} font-medium text-sm`}
        >
          <span>Explore SDG {relatedSDG.number} Programs</span>
          <ChevronRight className="h-4 w-4" />
        </Link>
      </div>
    </div>
  );
}

export default function RelatedSDGsSection({ sdg }: RelatedSDGsSectionProps) {
  if (sdg.relatedSDGs.length === 0) {
    return null;
  }

  return (
    <div className="py-16 px-5 md:px-10 lg:px-16 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col gap-2 mb-12 text-center">
          <div className="inline-flex mx-auto items-center gap-2 bg-primary/10 py-1 px-3 rounded-full w-fit mb-4">
            <span className="text-sm font-medium text-primary">
              Connected Goals
            </span>
          </div>
          <h2 className="font-bold text-3xl md:text-4xl mb-4">Related SDGs</h2>
          <p className="text-base md:text-lg text-gray-700 max-w-3xl mx-auto">
            Our {sdg.title} work connects with these other Sustainable
            Development Goals.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {sdg.relatedSDGs.map((relatedSDG) => (
            <RelatedSDGCard key={relatedSDG.number} relatedSDG={relatedSDG} />
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-gray-600 mb-4">
            The UN Sustainable Development Goals are interconnected, and
            progress on one accelerates progress on others.
          </p>
          <a
            href="https://sdgs.un.org/goals"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block bg-primary text-white px-4 py-2 rounded hover:bg-primary/90 transition-colors"
          >
            Learn About All 17 SDGs
          </a>
        </div>
      </div>
    </div>
  );
}
