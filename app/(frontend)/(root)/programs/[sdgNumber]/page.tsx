import SDGPage from "./components/SDGPage";

// This ensures proper export of dynamic params for server-side rendering
export function generateStaticParams() {
  return [
    { sdgNumber: "sdg-3" },
    { sdgNumber: "sdg-4" },
    { sdgNumber: "sdg-5" },
    { sdgNumber: "sdg-13" },
  ];
}

export default async function Page({
  params,
}: {
  params: Promise<{ sdgNumber: string }>;
}) {
  // Get the sdgNumber directly from params (no need to await)
  const sdgNumber = (await params).sdgNumber;

  // Pass the value to your component
  return <SDGPage sdgNumber={sdgNumber} />;
}
