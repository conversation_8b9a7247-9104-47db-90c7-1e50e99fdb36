import { Metadata } from "next";
import React from "react";
import HeroSection from "@/components/blog-and-news/HeroSection";
import FeaturedPostSection from "@/components/blog-and-news/FeaturedPostSection";
import BlogsWithFilters from "@/components/blog-and-news/BlogsWithFilters";
import NewsletterSignup from "@/components/blog-and-news/NewsletterSignup";
import { generateSEOMetadata, generateBreadcrumbSchema } from "@/lib/seo";

export const metadata: Metadata = generateSEOMetadata({
  title: "Blog & News - Insights on Women in Tech & Career Development",
  description:
    "Stay updated with the latest insights on women in technology, career development tips, success stories, and industry trends. Read inspiring articles about coding, financial literacy, mental health, and gender equality in STEM.",
  keywords: [
    "women in tech blog",
    "coding tutorials",
    "career development tips",
    "tech industry insights",
    "success stories",
    "financial literacy articles",
    "mental health resources",
    "gender equality STEM",
    "technology trends",
    "professional development",
    "women empowerment blog",
    "tech career advice",
  ],
  canonical: "/blogs-and-news",
});

export default function blogandnews() {
  return (
    <main className="min-h-screen bg-white">
      <HeroSection />
      <FeaturedPostSection />
      <BlogsWithFilters />
      <NewsletterSignup />
    </main>
  );
}
