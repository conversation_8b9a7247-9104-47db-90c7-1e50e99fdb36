import {
  Calendar,
  Clock,
  ArrowLeft,
  Tag,
  Share2,
  Heart,
  MessageCircle,
  User,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { notFound } from "next/navigation";
import { blogPosts, BlogPost } from "@/data/blogData";
import { Metadata } from "next";

// Function to get blog post by ID
const getBlogById = (id: string): BlogPost | undefined => {
  return blogPosts.find((post) => post.id === id);
};

// Generate static params for all blog posts
export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    blog: post.id,
  }));
}

// Generate metadata for each blog post
export async function generateMetadata({
  params,
}: {
  params: Promise<{ blog: string }>;
}): Promise<Metadata> {
  const { blog } = await params;
  const post = getBlogById(blog);

  if (!post) {
    return {
      title: "Blog Post Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  return {
    title: `${post.title} | GirlCode Movement`,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: [post.coverImage],
      type: "article",
      publishedTime: post.date,
      authors: [post.author],
      tags: post.tags,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [post.coverImage],
    },
  };
}

// Update the component props type to match Next.js 15 requirements
interface BlogPageProps {
  params: Promise<{
    blog: string;
  }>;
}

export default async function BlogPage({ params }: BlogPageProps) {
  const { blog: blogId } = await params;
  const post = getBlogById(blogId);

  // Not found state
  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Cover Image */}
      <div className="relative h-[60vh] min-h-[500px] overflow-hidden">
        <Image
          src={post.coverImage}
          alt={post.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20" />

        {/* Navigation */}
        <div className="absolute top-0 left-0 right-0 z-10">
          <div className="max-w-6xl mx-auto px-6 py-6">
            <Link
              href="/blogs-and-news"
              className="inline-flex items-center text-white/90 hover:text-white transition-colors group"
            >
              <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
              Back to Blog
            </Link>
          </div>
        </div>

        {/* Hero Content */}
        <div className="absolute bottom-0 left-0 right-0 text-white">
          <div className="max-w-6xl mx-auto px-6 pb-12">
            {/* Category Badge */}
            <div className="mb-4">
              <span className="inline-block px-4 py-2 bg-primary text-white text-sm font-semibold rounded-full">
                {post.category}
              </span>
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight max-w-4xl">
              {post.title}
            </h1>

            {/* Excerpt */}
            <p className="text-xl text-white/90 mb-8 max-w-3xl leading-relaxed">
              {post.excerpt}
            </p>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-white/80">
              <div className="flex items-center gap-3">
                <Image
                  src={post.authorImage}
                  alt={post.author}
                  width={40}
                  height={40}
                  className="rounded-full ring-2 ring-white/20"
                />
                <div>
                  <div className="font-medium text-white">{post.author}</div>
                  <div className="text-sm text-white/70">Author</div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {post.date}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {post.readTime}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
          {/* Article Content */}
          <div className="lg:col-span-3">
            <article className="prose prose-lg max-w-none">
              {post.content ? (
                <div
                  className="prose prose-lg max-w-none 
                    prose-headings:text-gray-900 prose-headings:font-bold 
                    prose-h2:text-3xl prose-h2:mt-12 prose-h2:mb-6 
                    prose-h3:text-2xl prose-h3:mt-10 prose-h3:mb-4 
                    prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-6 prose-p:text-lg
                    prose-a:text-primary prose-a:no-underline hover:prose-a:underline
                    prose-strong:text-gray-900 prose-em:text-gray-600
                    prose-ul:text-gray-700 prose-ol:text-gray-700 prose-li:mb-2
                    prose-blockquote:border-l-4 prose-blockquote:border-primary 
                    prose-blockquote:bg-gray-50 prose-blockquote:px-6 prose-blockquote:py-4"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />
              ) : (
                <div className="space-y-6 text-gray-700 text-lg leading-relaxed">
                  <p>
                    This article explores {post.title.toLowerCase()} and its
                    impact on our community. Through our programs and
                    initiatives, we've seen firsthand how this topic affects the
                    women and girls we serve.
                  </p>
                  <p>
                    Our approach focuses on practical solutions and
                    community-driven strategies that empower individuals to
                    create positive change in their lives and communities.
                  </p>
                  <p>
                    To learn more about this topic and how you can get involved
                    in making a difference, contact our team or explore our
                    related programs.
                  </p>
                </div>
              )}
            </article>

            {/* Article Footer */}
            <div className="mt-16 pt-8 border-t border-gray-200">
              {/* Tags */}
              <div className="mb-8">
                <h3 className="font-semibold text-gray-900 mb-4">Tags</h3>
                <div className="flex flex-wrap gap-3">
                  {post.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors"
                    >
                      <Tag className="w-3 h-3" />
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* Engagement Actions */}
              <div className="bg-gray-50 rounded-2xl p-6 mb-8">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-6">
                    <button className="flex items-center gap-2 text-gray-600 hover:text-red-500 transition-colors">
                      <Heart className="w-5 h-5" />
                      <span className="font-medium">42</span>
                    </button>
                    <button className="flex items-center gap-2 text-gray-600 hover:text-blue-500 transition-colors">
                      <MessageCircle className="w-5 h-5" />
                      <span className="font-medium">12</span>
                    </button>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-500">Share:</span>
                    <SocialShareButtons post={post} />
                  </div>
                </div>
              </div>

              {/* Author Bio */}
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8">
                <div className="flex items-start gap-6">
                  <Image
                    src={post.authorImage}
                    alt={post.author}
                    width={80}
                    height={80}
                    className="rounded-full ring-4 ring-white"
                  />
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {post.author}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Contributing writer at GirlCode Movement, passionate about
                      empowering women through technology and creating
                      meaningful change in communities.
                    </p>
                    <div className="flex items-center gap-2 text-primary font-medium">
                      <User className="w-4 h-4" />
                      <span className="text-sm">Author & Advocate</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Related Articles */}
            <div className="sticky top-8">
              <div className="bg-white rounded-2xl border border-gray-200 p-6 mb-6">
                <h3 className="font-bold text-gray-900 mb-6">
                  Related Articles
                </h3>
                <div className="space-y-6">
                  {blogPosts
                    .filter(
                      (relatedPost) =>
                        relatedPost.id !== post.id &&
                        (relatedPost.category === post.category ||
                          relatedPost.tags.some((tag) =>
                            post.tags.includes(tag)
                          ))
                    )
                    .slice(0, 3)
                    .map((relatedPost) => (
                      <Link
                        href={`/blogs-and-news/${relatedPost.id}`}
                        key={relatedPost.id}
                        className="block group"
                      >
                        <div className="flex gap-4">
                          <div className="relative w-20 h-20 rounded-xl overflow-hidden flex-shrink-0">
                            <Image
                              src={relatedPost.coverImage}
                              alt={relatedPost.title}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-gray-900 group-hover:text-primary transition-colors line-clamp-2 mb-2">
                              {relatedPost.title}
                            </h4>
                            <div className="flex items-center gap-1 text-xs text-gray-500">
                              <Calendar className="w-3 h-3" />
                              {relatedPost.date}
                            </div>
                          </div>
                        </div>
                      </Link>
                    ))}
                </div>
              </div>

              {/* Newsletter Signup */}
              <div className="bg-gradient-to-br from-primary to-primary/80 rounded-2xl p-6 text-white">
                <h3 className="font-bold mb-3">Stay Updated</h3>
                <p className="text-white/90 text-sm mb-4">
                  Get the latest articles and updates from GirlCode Movement.
                </p>
                <div className="space-y-3">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="w-full px-4 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/20"
                  />
                  <button className="w-full px-4 py-2 bg-white text-primary font-medium rounded-lg hover:bg-gray-100 transition-colors">
                    Subscribe
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* More Articles Section */}
      <div className="bg-gray-50 py-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900">More Articles</h2>
            <Link
              href="/blogs-and-news"
              className="flex items-center gap-1 text-primary font-medium hover:gap-2 transition-all"
            >
              View All
              <ChevronRight className="w-4 h-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts
              .filter((p) => p.id !== post.id)
              .slice(0, 3)
              .map((article) => (
                <Link
                  href={`/blogs-and-news/${article.id}`}
                  key={article.id}
                  className="group"
                >
                  <article className="bg-white rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-300 group-hover:-translate-y-1">
                    <div className="relative h-48">
                      <Image
                        src={article.coverImage}
                        alt={article.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-6">
                      <div className="text-primary text-sm font-medium mb-2">
                        {article.category}
                      </div>
                      <h3 className="font-bold text-gray-900 mb-3 group-hover:text-primary transition-colors line-clamp-2">
                        {article.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {article.excerpt}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {article.date}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {article.readTime}
                        </div>
                      </div>
                    </div>
                  </article>
                </Link>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Social Share Buttons Component
function SocialShareButtons({ post }: { post: BlogPost }) {
  const shareUrl = typeof window !== "undefined" ? window.location.href : "";
  const shareText = encodeURIComponent(post.title);

  return (
    <div className="flex items-center gap-2">
      <a
        href={`https://twitter.com/intent/tweet?text=${shareText}&url=${encodeURIComponent(
          shareUrl
        )}`}
        target="_blank"
        rel="noopener noreferrer"
        className="p-2 text-gray-600 hover:text-black hover:bg-gray-100 rounded-lg transition-colors"
        title="Share on Twitter"
      >
        <Share2 className="w-4 h-4" />
      </a>
      <a
        href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
          shareUrl
        )}`}
        target="_blank"
        rel="noopener noreferrer"
        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
        title="Share on Facebook"
      >
        <Share2 className="w-4 h-4" />
      </a>
      <a
        href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
          shareUrl
        )}`}
        target="_blank"
        rel="noopener noreferrer"
        className="p-2 text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
        title="Share on LinkedIn"
      >
        <Share2 className="w-4 h-4" />
      </a>
    </div>
  );
}
